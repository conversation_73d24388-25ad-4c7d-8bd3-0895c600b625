import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';

import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { CartService, CartItem } from '../../services/cart.service';
import { DataService, Artist } from '../../services/data.service';
import { ArtistApplicationFormComponent } from '../../components/shared/artist-application-form/artist-application-form.component';

@Component({
  selector: 'app-artists',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaDecorativeElementComponent,
    ArtistApplicationFormComponent
  ],
  templateUrl: './artists.component.html',
  styleUrl: './artists.component.css'
})
export class ArtistsComponent implements OnInit {
  isArtistApplicationFormOpen = false;
  featuredArtists: (Artist & { expanded?: boolean })[] = [];
  expandedArtists = new Set<string>();

  constructor(
    private cartService: CartService,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.featuredArtists = this.dataService.getFeaturedArtists().map(artist => ({
      ...artist,
      expanded: false
    }));
  }

  toggleArtistDescription(artist: Artist & { expanded?: boolean }) {
    if (this.expandedArtists.has(artist.id)) {
      this.expandedArtists.delete(artist.id);
      artist.expanded = false;
    } else {
      this.expandedArtists.add(artist.id);
      artist.expanded = true;
    }
  }
  addToCart(artwork: any, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const cartItem: Omit<CartItem, 'quantity'> = {
      id: artwork.id,
      name: artwork.name,
      slug: artwork.slug,
      price: artwork.price,
      image: artwork.image,
      artist: artwork.artist
    };

    this.cartService.addToCart(cartItem, 1);
  }

  isInCart(artworkId: string): boolean {
    return this.cartService.isInCart(artworkId);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  openArtistApplicationForm() {
    this.isArtistApplicationFormOpen = true;
  }

  closeArtistApplicationForm() {
    this.isArtistApplicationFormOpen = false;
  }

  onArtistApplicationSubmit(formData: any) {
    console.log('Artist application submitted:', formData);
    // Here you would typically send the data to your backend
    this.closeArtistApplicationForm();
  }
}
