<!-- Enhanced <PERSON> Banner with <PERSON><PERSON><PERSON> Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div
    class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
    style="background-image: url('https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg')">
  </div>

  <!-- Animated Gradient Background -->
  <div class="absolute inset-0 overflow-hidden animate-gradient-background">
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>
    <!-- Animated Gradient Accent -->
    <div
      class="absolute inset-0 bg-gradient-to-tr from-accent-500/20 via-turmeric-400/10 to-transparent animate-gradient-shift">
    </div>

    <!-- <PERSON><PERSON><PERSON> Background -->
    <div class="absolute inset-0 opacity-10">
      <app-mithila-art-background [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" opacity="15">
      </app-mithila-art-background>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-20 w-32 h-32 rounded-full border border-white/20 opacity-30 animate-float-slow">
    </div>
    <div
      class="absolute bottom-20 left-10 w-24 h-24 rounded-full border border-white/20 opacity-20 animate-float-medium">
    </div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'full'"
    position="top-6 left-6 right-6 bottom-6">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
      position="relative mb-6" classes="opacity-90 animate-float-slow" size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      About Mithilani Ghar
    </h1>

    <!-- Decorative Divider -->
    <div class="relative h-1 mx-auto mb-6 w-48 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-turmeric-300 via-white to-turmeric-300 rounded-full shadow-lg">
      </div>
      <div class="absolute inset-0 bg-white/30 animate-shimmer"></div>
    </div>

    <!-- Subtitle with Animation -->
    <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto animate-fade-in-delay">
      Preserving and promoting the rich artistic heritage of Mithila
    </p>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    </div>
  </div>
</div>

<!-- Our Story Section -->
<app-mithila-section primaryColor="#C1440E" secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div
          class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
        </div>

        <div class="relative rounded-lg overflow-hidden">
          <img [src]="aboutUsInfo?.story?.image || 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'"
            [alt]="aboutUsInfo?.story?.title || 'Mithilani Ghar Story'" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          </div>
        </div>

        <!-- Decorative Elements -->
        <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
          position="absolute -top-5 -right-5" classes="opacity-80 animate-float-slow pointer-events-none" size="40px">
        </app-mithila-decorative-element>
      </div>
    </div>
    <div>
      <app-section-title [title]="aboutUsInfo?.story?.title || 'Our Story'" subtitle="How Mithilani Ghar came to be"
        alignment="left"></app-section-title>

      <div class="mt-6 space-y-4 text-gray-800">
        <p *ngFor="let paragraph of aboutUsInfo?.story?.content || []" class="text-base sm:text-lg leading-relaxed">
          {{paragraph}}
        </p>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Our Mission Section -->
<app-mithila-section primaryColor="#F4B400" secondaryColor="#C1440E"
  backgroundGradient="from-secondary-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title [title]="aboutUsInfo?.mission?.title || 'Our Mission & Values'" subtitle="What drives us every day"></app-section-title>

  <!-- Mission Statement -->
  <div class="max-w-4xl mx-auto text-center mb-12">
    <p class="text-lg text-gray-700 leading-relaxed">
      {{aboutUsInfo?.mission?.content || 'Our mission is to preserve, promote, and perpetuate the rich artistic heritage of Mithila.'}}
    </p>
  </div>

  <!-- Values Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
    <div *ngFor="let value of aboutUsInfo?.mission?.values || []; let i = index"
      class="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 border-primary-500 relative overflow-hidden group">
      <!-- Decorative Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000">
      </div>

      <div class="relative z-10">
        <div
          class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 text-primary-600 mb-4 group-hover:bg-primary-200 transition-colors duration-300">
          <span class="text-lg font-bold">{{i + 1}}</span>
        </div>
        <p class="text-gray-700 leading-relaxed">{{value}}</p>
      </div>

      <!-- Decorative Element -->
      <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'geometric'"
        position="absolute -bottom-4 -right-4"
        classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none" size="30px">
      </app-mithila-decorative-element>
    </div>
  </div>
</app-mithila-section>

<!-- Our Team Section -->
<app-mithila-section primaryColor="#264653" secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-success-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Meet Our Team"
    subtitle="The passionate individuals behind Mithilani Ghar"></app-section-title>

  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let member of aboutUsInfo?.team; let i = index"
      class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

      <!-- Full Width Profile Image -->
      <div class="relative h-48 overflow-hidden">
        <img [src]="member.image" [alt]="member.name"
             class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">

        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <!-- Member Details - Half Card Style -->
      <div class="p-6">
        <div class="text-center mb-4">
          <h3 class="text-xl font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">
            {{member.name}}
          </h3>
          <p class="text-primary-600 font-medium mb-2">{{member.role}}</p>
          <div class="text-gray-600 text-sm">
            <p [class.line-clamp-2]="!member.expanded">{{member.bio}}</p>
            <button
              *ngIf="member.bio.length > 100"
              (click)="toggleTeamMemberDescription(member)"
              class="text-primary-600 hover:text-primary-700 text-xs font-medium mt-1 transition-colors duration-200">
              {{member.expanded ? 'Show Less' : 'Read More'}}
            </button>
          </div>
        </div>

        <!-- Social Media Links -->
        <div class="flex justify-center space-x-4">
          <a href="https://facebook.com" target="_blank"
            class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
            </svg>
          </a>

          <a href="https://instagram.com" target="_blank"
            class="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>

          <a href="#" target="_blank"
            class="w-10 h-10 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-gray-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Achievements Section -->
<app-mithila-section primaryColor="#E76F51" secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Achievements"
    subtitle="Milestones in our journey of preserving Mithila art"></app-section-title>

  <div class="mt-12">
    <div class="relative">
      <!-- Timeline Line -->
      <div
        class="absolute left-0 md:left-1/2 h-full w-1 bg-gradient-to-b from-primary-100 via-primary-300 to-primary-100 rounded-full transform -translate-x-1/2">
      </div>

      <!-- Timeline Items -->
      <div class="space-y-16">
        <div *ngFor="let achievement of aboutUsInfo?.achievements; let i = index; let odd = odd"
             class="relative flex flex-col md:flex-row items-center">

          <!-- Content (left side for odd items, right side for even items) -->
          <div class="flex items-center"
               [ngClass]="odd ? 'order-1 md:w-1/2 md:pr-12 md:text-right' : 'order-2 md:order-1 md:w-1/2 md:pr-12 md:text-right mt-6 md:mt-0'">
            <div class="w-full">
              <div class="mb-4">
                <span class="inline-block px-4 py-1 bg-primary-100 text-primary-600 rounded-full text-sm font-medium">
                  {{achievement.year}}
                </span>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                {{achievement.title}}
              </h3>
              <p class="text-gray-700">{{achievement.description}}</p>
            </div>
          </div>

          <!-- Timeline Node -->
          <div class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-primary-500 to-brick-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">{{i + 1}}</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-primary-500 opacity-50 animate-ping-slow"></span>
          </div>

          <!-- Image (right side for odd items, left side for even items) -->
          <div [ngClass]="odd ? 'order-2 md:w-1/2 md:pl-12 mt-6 md:mt-0' : 'order-1 md:order-2 md:w-1/2 md:pl-12'">
            <div class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-brick-300 to-primary-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
                  [alt]="achievement.title"
                  class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div class="absolute inset-0 bg-gradient-to-t from-primary-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#E76F51'" [type]="'lotus'"
                position="absolute -bottom-4 -right-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Our Facilities Section -->
<app-mithila-section primaryColor="#D81B60" secondaryColor="#008C8C"
  backgroundGradient="from-magenta-50 via-background-light to-peacock-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Facilities"
    subtitle="Experience the world of Mithila art at our center"></app-section-title>

  <!-- Facilities Showcase -->
  <div class="mt-12">
    <div *ngFor="let facility of aboutUsInfo?.facilities; let i = index; let odd = odd"
         class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16">

      <!-- Content -->
      <div [ngClass]="{'order-2 lg:order-1': odd, 'order-1': !odd}">
        <div class="relative mb-4">
          <div class="inline-block relative">
            <span class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-primary-400/80 to-primary-500/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md">
              {{facility.name}}
            </span>
            <!-- Animated Glow Effect -->
            <div class="absolute -inset-1 bg-gradient-to-r from-primary-300/0 via-primary-300/40 to-primary-300/0 rounded-full blur-md animate-pulse-slow -z-10">
            </div>
          </div>
        </div>

        <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">{{facility.name}}</h3>

        <div class="space-y-4 text-gray-700">
          <p>{{facility.description}}</p>

          <ul class="mt-4 space-y-2" *ngIf="facility.features">
            <li *ngFor="let feature of facility.features" class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>{{feature}}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Image -->
      <div [ngClass]="{'order-1 lg:order-2': odd, 'order-2': !odd}">
        <div class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
          <!-- Decorative Border -->
          <div class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-primary-300 rounded-lg blur-sm animate-border-gradient">
          </div>

          <div class="relative rounded-lg overflow-hidden">
            <img [src]="facility.image" [alt]="facility.name"
              class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

            <!-- Overlay Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            </div>
          </div>

          <!-- Decorative Element -->
          <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'geometric'"
            position="absolute -bottom-4 -right-4"
            classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none" size="40px">
          </app-mithila-decorative-element>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>