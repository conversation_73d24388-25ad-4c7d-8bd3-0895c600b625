<!-- Lightbox Overlay -->
<div *ngIf="isOpen" 
     class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
     [@fadeInOut]
     (click)="onBackdropClick($event)">
  
  <!-- Close Button -->
  <button 
    class="absolute top-4 right-4 z-60 text-white hover:text-gray-300 transition-colors duration-200"
    (click)="closeLightbox()"
    aria-label="Close lightbox">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  </button>

  <!-- Navigation Buttons -->
  <button *ngIf="hasPrevious"
    class="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors duration-200 p-2"
    (click)="previousImage()"
    aria-label="Previous image">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
    </svg>
  </button>

  <button *ngIf="hasNext"
    class="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors duration-200 p-2"
    (click)="nextImage()"
    aria-label="Next image">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
  </button>

  <!-- Main Content -->
  <div class="max-w-7xl max-h-full mx-4 flex flex-col items-center" [@slideIn]>
    <!-- Image Container -->
    <div class="relative flex-1 flex items-center justify-center mb-4">
      <img *ngIf="currentImage"
           [src]="currentImage.src"
           [alt]="currentImage.alt"
           class="max-w-full lightbox-max-height object-contain shadow-2xl"
           (load)="onImageLoad()"
           (error)="onImageError()">
      
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="absolute inset-0 flex items-center justify-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    </div>

    <!-- Image Info -->
    <div *ngIf="currentImage" class="relative w-full max-w-4xl">
      <!-- Image Title and Basic Info -->
      <div class="text-center text-white mb-4">
        <h3 *ngIf="currentImage.title" class="text-xl md:text-2xl font-bold mb-2">{{ currentImage.title }}</h3>
        <div class="flex flex-col sm:flex-row items-center justify-center gap-2 text-sm md:text-base">
          <p *ngIf="currentImage.artist" class="text-yellow-300 font-medium">By {{ currentImage.artist }}</p>
          <span *ngIf="currentImage.artist && currentImage.category" class="hidden sm:inline text-gray-400">•</span>
          <p *ngIf="currentImage.category" class="text-gray-300">{{ currentImage.category }}</p>
        </div>
      </div>

      <!-- Info Button -->
      <div class="flex justify-center">
        <button
          class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-200 shadow-lg"
          (click)="toggleDescription()"
          [attr.aria-label]="showDescription ? 'Hide description' : 'Show description'">
          <svg class="w-6 h-6 text-white transition-transform duration-200"
               [class.rotate-180]="showDescription"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>

      <!-- Description Panel -->
      <div *ngIf="showDescription && currentImage"
           class="mt-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 md:p-6 text-gray-900 shadow-xl max-w-2xl mx-auto"
           [@fadeInOut]>
        <div class="flex justify-between items-start mb-3">
          <h4 class="font-bold text-lg md:text-xl text-gray-900">{{ currentImage.title }}</h4>
          <button
            class="text-gray-500 hover:text-gray-700 ml-2 p-1"
            (click)="toggleDescription()"
            aria-label="Close description">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-3">
          <div *ngIf="currentImage.artist" class="flex items-center">
            <span class="text-sm font-medium text-gray-600 w-20">Artist:</span>
            <span class="text-primary-600 font-medium">{{ currentImage.artist }}</span>
          </div>

          <div *ngIf="currentImage.category" class="flex items-center">
            <span class="text-sm font-medium text-gray-600 w-20">Category:</span>
            <span class="text-gray-800">{{ currentImage.category }}</span>
          </div>

          <div *ngIf="currentImage.description" class="pt-2 border-t border-gray-200">
            <p class="text-gray-700 text-sm md:text-base leading-relaxed">{{ currentImage.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Counter -->
    <div class="text-white text-sm mt-4">
      {{ currentIndex + 1 }} of {{ images.length }}
    </div>

    <!-- Thumbnail Navigation -->
    <div *ngIf="images.length > 1" class="flex space-x-2 mt-4 max-w-full overflow-x-auto pb-2">
      <button *ngFor="let image of images; let i = index"
              class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200"
              [class.border-white]="i === currentIndex"
              [class.border-gray-600]="i !== currentIndex"
              [class.opacity-100]="i === currentIndex"
              [class.opacity-60]="i !== currentIndex"
              (click)="goToImage(i)">
        <img [src]="image.src" [alt]="image.alt" class="w-full h-full object-cover">
      </button>
    </div>
  </div>

  <!-- Keyboard Instructions -->
  <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm text-center opacity-70">
    <p>Use arrow keys to navigate • Press ESC to close</p>
  </div>
</div>
