<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
    style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
      position="relative mb-6" classes="opacity-90" size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Our Artists
    </h1>

    <!-- Subtitle -->
    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists and shop their beautiful creations
    </p>
  </div>
</div>

<!-- Artists Section -->
<app-mithila-section primaryColor="#F4B400" secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Artists"
    subtitle="Meet our talented artists who specialize in traditional Mithila art forms"></app-section-title>

  <!-- Artist Profile Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artist of featuredArtists"
      class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

      <!-- Full Width Profile Image -->
      <div class="relative h-64 overflow-hidden">
        <img [src]="artist.image" [alt]="artist.name"
             class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">

        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <!-- Artist Details - Half Card Style -->
      <div class="p-6">
        <div class="text-center mb-4">
          <h3 class="text-xl font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">{{artist.name}}</h3>
          <p class="text-primary-600 font-medium mb-2">{{artist.role}}</p>
          <p class="text-gray-600 text-sm line-clamp-2">{{artist.bio}}</p>
        </div>

        <!-- Social Media Links -->
        <div class="flex justify-center space-x-4">
          <a href="https://facebook.com" target="_blank"
            class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
            </svg>
          </a>

          <a href="https://instagram.com" target="_blank"
            class="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>

          <a href="#" target="_blank"
            class="w-10 h-10 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-gray-700 transition-all duration-300 transform hover:scale-110 shadow-md">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Spotlight Section -->
<app-mithila-section primaryColor="#264653" secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Artist Spotlight"
    subtitle="Featuring Sarita Devi, Founder & Master Artist"></app-section-title>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div
          class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
        </div>

        <div class="relative rounded-lg overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
            alt="Sarita Devi" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          </div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">Sarita Devi</h3>
      <p class="text-primary-600 mb-6 font-medium">Master Artist & Founder</p>

      <div class="space-y-4 text-gray-700">
        <p>Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques
          from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural
          traditions of the Mithila region.</p>
        <p>After completing her formal education, she dedicated herself to mastering and preserving this ancient art
          form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work,
          teach, and showcase their creations.</p>
        <p>Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often
          explores themes of rural life, mythology, and women's experiences.</p>
      </div>

      <div class="mt-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Notable Achievements</h4>
        <ul class="space-y-2 text-gray-700">
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>National Award for Excellence in Traditional Arts (2018)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Featured artist at the South Asian Art Festival, London (2019)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Published in "Contemporary Folk Artists of South Asia" (2020)</span>
          </li>
        </ul>
      </div>

      <div class="mt-8">
        <a [routerLink]="['/artists', '1']"
          class="inline-block px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          View Full Profile
        </a>
      </div>
    </div>
  </div>


  <div class="bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 backdrop-blur-md rounded-2xl p-8 border border-primary-500/30 shadow-xl">
    <h3 class="text-2xl font-bold mb-6 text-white">Ready to Join Us?</h3>
    <p class="text-white/95 mb-8 text-lg leading-relaxed">
      We're looking for passionate artists who specialize in traditional Mithila art forms.
      Whether you're experienced or just starting, we welcome artists who are dedicated to preserving and promoting this
      beautiful art form.
    </p>

    <div class="flex flex-wrap justify-center gap-4">
      <button (click)="openArtistApplicationForm()"
        class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
            </path>
          </svg>
          Apply to Join as Artist
        </span>
      </button>
      <a routerLink="/contact"
        class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
            </path>
          </svg>
          Contact Us First
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Application Form Modal -->
<app-artist-application-form [isOpen]="isArtistApplicationFormOpen" (close)="closeArtistApplicationForm()"
  (submit)="onArtistApplicationSubmit($event)">
</app-artist-application-form>