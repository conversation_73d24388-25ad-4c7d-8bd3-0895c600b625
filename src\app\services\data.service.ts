import { Injectable } from '@angular/core';

// Data Interfaces
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  artist: string | string[]; // Support multiple artists
  dimensions: string;
  materials: string[];
  featured: boolean;
  detailedDescription?: string;
  culturalSignificance?: string;
}

export interface Artist {
  id: string;
  name: string;
  role: string;
  bio: string;
  longBio?: string;
  image: string;
  coverImage?: string;
  category: string;
  featured: boolean;
  spotlight?: boolean;
  workshops?: boolean;
  specialization?: string;
  experience?: string;
  education?: string;
  awards?: string[];
  exhibitions?: string[];
  quote?: string;
  socialMedia?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
    website?: string;
  };
  artworks?: Artwork[];
}

export interface Artwork {
  id: string;
  name: string;
  slug: string;
  price: number;
  image: string;
  artist: string;
  category?: string;
  description?: string;
}

export interface LightboxImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
  artist?: string;
  category?: string;
}



export interface FAQItem {
  question: string;
  answer: string;
}

export interface ContactInfo {
  phone: string[];
  email: string;
  address: string;
  hours: string;
}

export interface Testimonial {
  id: number;
  name: string;
  role: string;
  image: string;
  quote: string;
}

export interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  imageUrl: string;
  description: string;
}

export interface CompanyInfo {
  name: string;
  tagline: string;
  description: string;
  mission: string;
  heroImage: string;
  features: {
    title: string;
    description: string;
    icon: string;
    color: string;
  }[];
}

export interface AboutUsInfo {
  story: {
    title: string;
    content: string[];
    image: string;
  };
  mission: {
    title: string;
    content: string;
    values: string[];
  };
  team: {
    id: string;
    name: string;
    role: string;
    bio: string;
    image: string;
    specialization?: string;
  }[];
  achievements: {
    year: string;
    title: string;
    description: string;
  }[];
  facilities: {
    name: string;
    description: string;
    image: string;
    features: string[];
  }[];
}

// Art Styles and Categories
export const ART_STYLES = [
  'Traditional Madhubani',
  'Contemporary Mithila',
  'Kohbar Art',
  'Bharni Style',
  'Katchni Style',
  'Tantrik Style',
  'Godna Style'
];

export const EXPERIENCE_LEVELS = [
  { value: 'beginner', label: 'Beginner (0-2 years)' },
  { value: 'intermediate', label: 'Intermediate (3-5 years)' },
  { value: 'advanced', label: 'Advanced (6-10 years)' },
  { value: 'expert', label: 'Expert (10+ years)' }
];

export const PRODUCT_CATEGORIES = [
  'All', 'Paintings', 'Clay Crafts', 'Textiles', 'Wood Crafts'
];

export const GALLERY_CATEGORIES = [
  'All', 'Traditional', 'Contemporary', 'Religious', 'Nature & Wildlife', 'Portraits', 'Abstract'
];

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor() { }

  // Contact Information
  getContactInfo(): ContactInfo {
    return {
      phone: ['+977-9814830580', '+977-9821762884'],
      email: '<EMAIL>',
      address: 'Barahbigha, Janaki Mandir Marg, Janakpurdham-08, Dhanusha, Nepal',
      hours: 'Daily 9:00 AM to 8:00 PM'
    };
  }



  // FAQ Items
  getFAQItems(): FAQItem[] {
    return [
      {
        question: 'What are your opening hours?',
        answer: 'We are open daily from 9:00 AM to 8:00 PM, including holidays.'
      },
      {
        question: 'Do you offer guided tours?',
        answer: 'Yes, we offer guided tours of our gallery and cultural center. Please contact us in advance to schedule a tour.'
      },
      {
        question: 'How can I purchase Mithila art?',
        answer: 'You can purchase art directly from our gallery or through our online shop. We ship worldwide and offer secure payment options.'
      },
      {
        question: 'Do you host cultural events?',
        answer: 'Yes, we regularly host cultural events, workshops, and exhibitions. Check our Events page or contact us for upcoming events.'
      },
      {
        question: 'Can I learn Mithila art at your center?',
        answer: 'Absolutely! We offer workshops and training programs for all skill levels, from beginners to advanced artists.'
      },
      {
        question: 'Do you ship internationally?',
        answer: 'Yes, we ship our artwork and crafts worldwide. Shipping costs and delivery times vary by location.'
      }
    ];
  }

  // Product Categories
  getProductCategories(): string[] {
    return [...PRODUCT_CATEGORIES];
  }

  // Gallery Categories
  getGalleryCategories(): string[] {
    return [...GALLERY_CATEGORIES];
  }

  // Art Styles
  getArtStyles(): string[] {
    return [...ART_STYLES];
  }

  // Experience Levels
  getExperienceLevels(): { value: string; label: string }[] {
    return [...EXPERIENCE_LEVELS];
  }

  // Products Data
  getProducts(): Product[] {
    return [
      {
        id: '1',
        name: 'Traditional Mithila Painting - Madhubani Art',
        slug: 'traditional-mithila-painting-madhubani-art',
        description: 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
        category: 'Paintings',
        price: 2500,
        images: [
          'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
          'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
        ],
        artist: 'Sarita Devi',
        dimensions: '16" x 12"',
        materials: ['Handmade Paper', 'Natural Pigments', 'Bamboo Brush'],
        featured: true,
        detailedDescription: 'This exquisite Madhubani painting represents the finest tradition of Mithila art. Hand-painted using natural pigments and traditional techniques passed down through generations.',
        culturalSignificance: 'Madhubani paintings are traditionally created by women in the Mithila region and often depict Hindu deities, nature, and social events.'
      },
      {
        id: '2',
        name: 'Handcrafted Clay Elephant',
        slug: 'handcrafted-clay-elephant',
        description: 'Beautiful clay elephant sculpture with intricate Mithila patterns, perfect for home decoration.',
        category: 'Clay Crafts',
        price: 1200,
        images: [
          'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
        ],
        artist: 'Ramesh Kumar',
        dimensions: '8" x 6" x 4"',
        materials: ['Natural Clay', 'Natural Pigments', 'Protective Coating'],
        featured: false,
        detailedDescription: 'This beautiful clay elephant is handcrafted using traditional techniques and decorated with authentic Mithila patterns.',
        culturalSignificance: 'Elephants are considered sacred in Hindu culture and are often depicted in Mithila art as symbols of wisdom and prosperity.'
      },
      {
        id: '3',
        name: 'Mithila Art Saree',
        slug: 'mithila-art-saree',
        description: 'Beautiful cotton saree with hand-painted Mithila designs, blending tradition with contemporary fashion.',
        category: 'Textiles',
        price: 4500,
        images: [
          'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
        ],
        artist: 'Kamala Devi',
        dimensions: '6 yards',
        materials: ['Pure Cotton', 'Natural Dyes', 'Hand Painting'],
        featured: true,
        detailedDescription: 'This elegant saree combines traditional Mithila art with contemporary fashion, featuring hand-painted designs on pure cotton.',
        culturalSignificance: 'Sarees with Mithila art represent the fusion of traditional craftsmanship with everyday wear.'
      },
      {
        id: '4',
        name: 'Wooden Jewelry Box with Mithila Art',
        slug: 'wooden-jewelry-box-mithila-art',
        description: 'Elegant wooden jewelry box decorated with traditional Mithila patterns and motifs.',
        category: 'Wood Crafts',
        price: 2200,
        images: [
          'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
        ],
        artist: 'Mohan Lal',
        dimensions: '10" x 8" x 4"',
        materials: ['Teak Wood', 'Natural Varnish', 'Hand Painting'],
        featured: false,
        detailedDescription: 'This elegant jewelry box is crafted from premium teak wood and decorated with intricate Mithila patterns.',
        culturalSignificance: 'Wooden crafts with Mithila art represent the versatility of this traditional art form across different mediums.'
      },
      {
        id: '5',
        name: 'Traditional Clay Water Pot',
        slug: 'traditional-clay-water-pot',
        description: 'Authentic clay water pot with beautiful Mithila designs, functional and decorative.',
        category: 'Clay Crafts',
        price: 800,
        images: [
          'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
        ],
        artist: 'Sunita Devi',
        dimensions: '12" x 10"',
        materials: ['Natural Clay', 'Traditional Glazing', 'Hand Painting'],
        featured: false,
        detailedDescription: 'This traditional water pot combines functionality with beauty, featuring authentic Mithila designs.',
        culturalSignificance: 'Clay water pots are essential household items in rural areas and are often decorated with protective and auspicious symbols.'
      },
      {
        id: '6',
        name: 'Handwoven Mithila Table Runner',
        slug: 'handwoven-mithila-table-runner',
        description: 'Elegant table runner with woven Mithila patterns, perfect for dining room decoration.',
        category: 'Textiles',
        price: 1800,
        images: [
          'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
        ],
        artist: ['Sunita Kumari', 'Kamala Devi'],
        dimensions: '72" x 14"',
        materials: ['Cotton Thread', 'Natural Dyes', 'Hand Weaving'],
        featured: false,
        detailedDescription: 'This handwoven table runner brings the beauty of Mithila art to your dining space.',
        culturalSignificance: 'Handwoven textiles with Mithila patterns are used in homes during festivals and special occasions.'
      }
    ];
  }

  // Get product by slug
  getProductBySlug(slug: string): Product | undefined {
    return this.getProducts().find(product => product.slug === slug);
  }

  // Get featured products
  getFeaturedProducts(): Product[] {
    return this.getProducts().filter(product => product.featured);
  }

  // Get products by category
  getProductsByCategory(category: string): Product[] {
    if (category === 'All') {
      return this.getProducts();
    }
    return this.getProducts().filter(product => product.category === category);
  }

  // Artists Data
  getArtists(): Artist[] {
    return [
      {
        id: '1',
        name: 'Sarita Devi',
        role: 'Master Artist & Founder',
        bio: 'With over 25 years of experience in traditional Mithila art, Sarita founded Mithilani Ghar to preserve and promote this unique cultural heritage.',
        longBio: 'Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural traditions of the Mithila region. After completing her formal education, she dedicated herself to mastering and preserving this ancient art form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work, teach, and showcase their creations. Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often explores themes of rural life, mythology, and women\'s experiences.',
        image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        category: 'Traditional',
        featured: true,
        spotlight: true,
        workshops: true,
        specialization: 'Traditional Madhubani Painting',
        experience: '25+ years',
        education: 'Self-taught, Traditional Family Training',
        awards: [
          'National Award for Excellence in Traditional Arts (2018)',
          'Featured artist at the South Asian Art Festival, London (2019)',
          'Published in "Contemporary Folk Artists of South Asia" (2020)'
        ],
        exhibitions: [
          'South Asian Art Festival, London (2019)',
          'Traditional Arts Exhibition, Kathmandu (2018)',
          'Cultural Heritage Showcase, New Delhi (2017)'
        ],
        quote: 'Art is not just about creating beauty; it\'s about preserving our cultural identity and passing it on to future generations.',
        socialMedia: {
          instagram: 'sarita_mithila_art',
          facebook: 'SaritaDeviArt',
          website: 'www.saritadeviart.com'
        },
        artworks: [
          {
            id: 'art-1',
            name: 'Traditional Fish Motif',
            slug: 'traditional-fish-motif',
            price: 15000,
            image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
            artist: 'Sarita Devi',
            category: 'Traditional',
            description: 'A beautiful traditional fish motif representing prosperity and fertility in Mithila culture.'
          },
          {
            id: 'art-2',
            name: 'Lotus Garden',
            slug: 'lotus-garden',
            price: 18000,
            image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
            artist: 'Sarita Devi',
            category: 'Traditional',
            description: 'Intricate lotus patterns symbolizing purity and spiritual awakening.'
          }
        ]
      },
      {
        id: '2',
        name: 'Kamala Devi',
        role: 'Contemporary Artist',
        bio: 'Kamala specializes in contemporary interpretations of traditional Mithila art, bringing modern perspectives to ancient techniques.',
        image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        category: 'Contemporary',
        featured: true,
        workshops: false,
        specialization: 'Contemporary Mithila Art',
        experience: '15 years',
        education: 'BFA from Lalit Kala Academy',
        awards: [
          'Young Artist Award, Nepal Art Council (2019)',
          'Innovation in Traditional Art Prize (2021)'
        ],
        exhibitions: [
          'Solo Exhibition: "Tradition Reimagined", Gallery Nepal, Kathmandu (2023)',
          'Asian Contemporary Art Fair, Tokyo (2022)',
          'Emerging Artists Showcase, Mumbai (2021)'
        ],
        quote: 'I believe in honoring our traditions while embracing the possibilities of contemporary expression.',
        socialMedia: {
          instagram: 'kamala_contemporary_art',
          twitter: 'KamalaDeviArt'
        }
      },
      {
        id: '3',
        name: 'Sunil Yadav',
        role: 'Environmental Artist',
        bio: 'Sunil combines traditional Mithila techniques with environmental themes, creating awareness about nature conservation through art.',
        image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
        category: 'Nature & Wildlife',
        featured: false,
        workshops: true,
        specialization: 'Environmental Mithila Art',
        experience: '12 years',
        education: 'Environmental Studies & Traditional Art Training',
        awards: [
          'Environmental Art Prize, WWF Nepal (2022)',
          'Conservation Through Art Grant (2021)',
          'Emerging Environmental Artist Award (2020)'
        ],
        exhibitions: [
          'Exhibition "Natural Heritage in Folk Art", National Museum (2023)',
          'Environmental Art Showcase, Kathmandu (2022)',
          'Biodiversity Through Art, Chitwan (2021)'
        ],
        quote: 'My art is a bridge between our cultural traditions and our natural environment. By depicting local wildlife and plants through Mithila art, I hope to remind people that our cultural heritage and natural heritage are deeply interconnected.',
        socialMedia: {
          instagram: 'sunil_nature_art',
          twitter: 'SunilYadavArt',
          facebook: 'SunilYadavNatureArt'
        }
      }
    ];
  }

  // Get artist by ID
  getArtistById(id: string): Artist | undefined {
    return this.getArtists().find(artist => artist.id === id);
  }

  // Get featured artists
  getFeaturedArtists(): Artist[] {
    return this.getArtists().filter(artist => artist.featured);
  }

  // Get spotlight artist
  getSpotlightArtist(): Artist | undefined {
    return this.getArtists().find(artist => artist.spotlight);
  }

  // Gallery Images Data
  getGalleryImages(): LightboxImage[] {
    return [
      {
        src: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        alt: 'Traditional Mithila Art',
        title: 'Madhubani Fish',
        artist: 'Sarita Devi',
        category: 'Traditional',
        description: 'A beautiful traditional Madhubani painting featuring fish motifs, symbolizing prosperity and good fortune.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        alt: 'Peacock Mithila Art',
        title: 'Royal Peacock',
        artist: 'Kamala Devi',
        category: 'Traditional',
        description: 'Intricate peacock design representing beauty and grace in traditional Mithila art style.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
        alt: 'Lotus Mithila Art',
        title: 'Sacred Lotus',
        artist: 'Sunita Jha',
        category: 'Religious',
        description: 'Sacred lotus motifs with intricate geometric patterns, representing purity and spiritual awakening.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
        alt: 'Tree of Life',
        title: 'Tree of Life',
        artist: 'Meera Sharma',
        category: 'Nature & Wildlife',
        description: 'A magnificent tree of life painting showcasing the connection between earth and sky.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        alt: 'Contemporary Mithila',
        title: 'Modern Fusion',
        artist: 'Priya Kumari',
        category: 'Contemporary',
        description: 'A contemporary interpretation of traditional Mithila art with modern color palettes.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        alt: 'Portrait Art',
        title: 'Village Woman',
        artist: 'Rekha Devi',
        category: 'Portraits',
        description: 'A beautiful portrait of a village woman adorned with traditional jewelry and clothing.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
        alt: 'Abstract Mithila',
        title: 'Geometric Dreams',
        artist: 'Anita Singh',
        category: 'Abstract',
        description: 'Abstract interpretation of traditional Mithila patterns with bold geometric designs.'
      },
      {
        src: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
        alt: 'Religious Art',
        title: 'Ganesha Blessing',
        artist: 'Sarita Devi',
        category: 'Religious',
        description: 'Lord Ganesha depicted in traditional Mithila style, bringing blessings and removing obstacles.'
      }
    ];
  }

  // Get gallery images by category
  getGalleryImagesByCategory(category: string): LightboxImage[] {
    if (category === 'All') {
      return this.getGalleryImages();
    }
    return this.getGalleryImages().filter(image => image.category === category);
  }

  // Home Page Data
  getCompanyInfo(): CompanyInfo {
    return {
      name: 'Mithilani Ghar',
      tagline: 'Preserving and Promoting the Rich Artistic Heritage of Mithila',
      description: 'Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region. Located in Janakpur, Nepal, we serve as an art gallery, craft store, and training center focused on traditional Mithila art forms.',
      mission: 'Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide, and ensure this unique cultural tradition continues to thrive for generations to come.',
      heroImage: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      features: [
        {
          title: 'Art Gallery',
          description: 'Showcasing authentic Mithila paintings and crafts',
          icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
          color: 'primary'
        },
        {
          title: 'Craft Store',
          description: 'Handmade products by skilled local artisans',
          icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
          color: 'secondary'
        },
        {
          title: 'Training Center',
          description: 'Learn traditional Mithila art techniques',
          icon: 'M12 14l9-5-9-5-9 5 9 5z',
          color: 'accent'
        },
        {
          title: 'Cultural Hub',
          description: 'Celebrating the heritage of Mithila region',
          icon: 'M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
          color: 'red'
        }
      ]
    };
  }

  // Testimonials
  getTestimonials(): Testimonial[] {
    return [
      {
        id: 1,
        name: 'Rajesh Sharma',
        role: 'Art Collector',
        image: 'https://randomuser.me/api/portraits/men/32.jpg',
        quote: 'The artwork I purchased from Mithilani Ghar is absolutely stunning. The attention to detail and vibrant colors truly capture the essence of Mithila art tradition.'
      },
      {
        id: 2,
        name: 'Sarah Johnson',
        role: 'Tourist',
        image: 'https://randomuser.me/api/portraits/women/44.jpg',
        quote: 'Visiting Mithilani Ghar was the highlight of my trip to Janakpur. The artists were so welcoming and I learned so much about the cultural significance behind each piece.'
      },
      {
        id: 3,
        name: 'Amit Patel',
        role: 'Interior Designer',
        image: 'https://randomuser.me/api/portraits/men/67.jpg',
        quote: 'I regularly source artwork from Mithilani Ghar for my clients. The quality is consistently excellent and the pieces add a unique cultural element to any space.'
      }
    ];
  }

  // Upcoming Events
  getUpcomingEvents(): Event[] {
    return [
      {
        id: '1',
        title: 'Mithila Art Workshop',
        date: 'June 15, 2025',
        time: '10:00 AM - 2:00 PM',
        location: 'Mithilani Ghar, Janakpur',
        imageUrl: 'https://i.etsystatic.com/43638819/r/il/7b65fd/5930357750/il_794xN.5930357750_3qzn.jpg',
        description: 'Learn the basics of traditional Mithila painting techniques from master artists.'
      },
      {
        id: '2',
        title: 'Exhibition: Modern Mithila',
        date: 'July 5-15, 2025',
        time: '9:00 AM - 8:00 PM',
        location: 'Mithilani Ghar Gallery',
        imageUrl: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
        description: 'A special exhibition showcasing contemporary interpretations of traditional Mithila art.'
      },
      {
        id: '3',
        title: 'Artist Talk: Preserving Heritage',
        date: 'July 20, 2025',
        time: '4:00 PM - 6:00 PM',
        location: 'Mithilani Ghar, Janakpur',
        imageUrl: 'https://i.etsystatic.com/43638819/r/il/7a099c/5978434699/il_794xN.5978434699_np8t.jpg',
        description: 'Join us for an insightful discussion on preserving cultural heritage through art.'
      }
    ];
  }

  // About Us Data
  getAboutUsInfo(): AboutUsInfo {
    return {
      story: {
        title: 'Our Story',
        content: [
          'Mithilani Ghar was born from a deep passion for preserving the rich artistic heritage of the Mithila region. Founded in 2015 by master artist Sarita Devi, our journey began in a small village near Janakpur, where traditional Mithila art has been practiced for centuries.',
          'What started as a humble initiative to support local artists has grown into a thriving cultural center that bridges the gap between traditional art forms and contemporary appreciation. We have become a beacon for artists, art enthusiasts, and cultural preservationists from around the world.',
          'Today, Mithilani Ghar stands as a testament to the enduring power of art to connect communities, preserve heritage, and inspire future generations. Our story continues to unfold with each brushstroke, each workshop, and each visitor who discovers the magic of Mithila art.'
        ],
        image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
      },
      mission: {
        title: 'Our Mission & Values',
        content: 'To preserve, promote, and perpetuate the rich artistic heritage of Mithila through authentic art creation, education, and cultural exchange while supporting local artists and their communities.',
        values: [
          'Authenticity - We maintain the traditional techniques and cultural significance of Mithila art',
          'Community - We support local artists and foster a sense of belonging and shared purpose',
          'Education - We share knowledge and skills to ensure the art form continues to thrive',
          'Innovation - We embrace contemporary interpretations while respecting traditional foundations',
          'Sustainability - We promote eco-friendly practices and sustainable livelihoods for artists'
        ]
      },
      team: [
        {
          id: '1',
          name: 'Sarita Devi',
          role: 'Founder & Master Artist',
          bio: 'With over 25 years of experience, Sarita is a renowned Mithila artist and the visionary behind Mithilani Ghar.',
          image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
          specialization: 'Traditional Madhubani Painting'
        },
        {
          id: '2',
          name: 'Ramesh Kumar',
          role: 'Gallery Manager',
          bio: 'Ramesh oversees our gallery operations and curates exhibitions that showcase the best of Mithila art.',
          image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
          specialization: 'Art Curation & Management'
        },
        {
          id: '3',
          name: 'Anita Jha',
          role: 'Education Director',
          bio: 'Anita leads our educational programs and workshops, sharing traditional techniques with new generations.',
          image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
          specialization: 'Art Education & Workshops'
        },
        {
          id: '4',
          name: 'Sunil Yadav',
          role: 'Community Outreach Coordinator',
          bio: 'Sunil connects with local communities and helps emerging artists join our collective.',
          image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
          specialization: 'Community Development'
        }
      ],
      achievements: [
        {
          year: '2015',
          title: 'Foundation of Mithilani Ghar',
          description: 'Established as a small art collective in Janakpur with 5 founding artists.'
        },
        {
          year: '2018',
          title: 'National Recognition',
          description: 'Received the National Award for Excellence in Traditional Arts preservation.'
        },
        {
          year: '2019',
          title: 'International Exhibition',
          description: 'Featured at the South Asian Art Festival in London, showcasing Mithila art globally.'
        },
        {
          year: '2020',
          title: 'Digital Expansion',
          description: 'Launched online platform, making Mithila art accessible worldwide during the pandemic.'
        },
        {
          year: '2022',
          title: 'Educational Partnership',
          description: 'Partnered with local schools to integrate traditional art education in curriculum.'
        },
        {
          year: '2024',
          title: 'Sustainability Initiative',
          description: 'Launched eco-friendly art materials program and sustainable artist livelihood project.'
        }
      ],
      facilities: [
        {
          name: 'Main Gallery',
          description: 'A spacious exhibition hall showcasing rotating displays of traditional and contemporary Mithila art.',
          image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
          features: [
            'Climate-controlled environment',
            'Professional lighting system',
            'Interactive display areas',
            'Guided tour facilities'
          ]
        },
        {
          name: 'Artist Studios',
          description: 'Dedicated workspace for resident and visiting artists to create their masterpieces.',
          image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
          features: [
            'Natural lighting',
            'Traditional art supplies',
            'Collaborative workspace',
            'Storage facilities'
          ]
        },
        {
          name: 'Workshop Center',
          description: 'Educational facility equipped for teaching traditional Mithila art techniques to students of all ages.',
          image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
          features: [
            'Flexible seating arrangements',
            'Audio-visual equipment',
            'Art supply stations',
            'Display boards'
          ]
        },
        {
          name: 'Cultural Library',
          description: 'A repository of books, documents, and digital archives related to Mithila culture and art history.',
          image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
          features: [
            'Historical documents',
            'Digital archives',
            'Research facilities',
            'Reading areas'
          ]
        }
      ]
    };
  }
}
