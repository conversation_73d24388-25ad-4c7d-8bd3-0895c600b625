<!-- Artist Spotlight Card -->
<div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
     [class.max-w-4xl]="cardStyle === 'default'"
     [class.max-w-2xl]="cardStyle === 'compact'">
  
  <!-- Full Width Profile Image -->
  <div class="relative overflow-hidden"
       [class.h-80]="cardStyle === 'default'"
       [class.h-64]="cardStyle === 'compact'">
    <img [src]="artist.image" [alt]="artist.name"
         class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">

    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>

  <!-- Artist Details -->
  <div class="p-6 md:p-8">
    <div class="text-center mb-6">
      <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">{{artist.name}}</h3>
      <p class="text-primary-600 font-medium mb-4 text-lg">{{artist.role}}</p>
      
      <!-- Description with Read More -->
      <div class="text-gray-600 text-sm mb-6">
        <p [class.line-clamp-3]="!expanded && cardStyle === 'compact'"
           [class.line-clamp-4]="!expanded && cardStyle === 'default'">{{artist.description}}</p>
        <button 
          *ngIf="artist.description.length > 200"
          (click)="toggleDescription()"
          class="text-primary-600 hover:text-primary-700 text-xs font-medium mt-2 transition-colors duration-200">
          {{expanded ? 'Show Less' : 'Read More'}}
        </button>
      </div>
    </div>

    <!-- Professional Details Grid -->
    <div *ngIf="showFullDetails" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          Specialization
        </h4>
        <p class="text-gray-700 text-sm">{{artist.specialization}}</p>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Experience
        </h4>
        <p class="text-gray-700 text-sm">{{artist.experience}}</p>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4 md:col-span-2">
        <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          Education
        </h4>
        <p class="text-gray-700 text-sm">{{artist.education}}</p>
      </div>
    </div>

    <!-- Quote -->
    <div *ngIf="showFullDetails && artist.quote" class="bg-primary-50 border-l-4 border-primary-500 p-4 mb-6">
      <blockquote class="italic text-gray-700 relative">
        <svg class="w-6 h-6 text-primary-300 absolute -top-2 -left-1" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
        </svg>
        <span class="ml-8">"{{artist.quote}}"</span>
      </blockquote>
    </div>

    <!-- Awards Section -->
    <div *ngIf="showFullDetails && artist.awards && artist.awards.length > 0" class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
        Awards & Recognition
      </h4>
      <div class="bg-yellow-50 rounded-lg p-4">
        <ul class="space-y-2">
          <li *ngFor="let award of artist.awards" class="flex items-start text-gray-700 text-sm">
            <span class="text-yellow-600 mr-2 mt-1 flex-shrink-0">🏆</span>
            <span>{{award}}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Exhibitions Section -->
    <div *ngIf="showFullDetails && artist.exhibitions && artist.exhibitions.length > 0" class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        Exhibitions
      </h4>
      <div class="bg-blue-50 rounded-lg p-4">
        <ul class="space-y-2">
          <li *ngFor="let exhibition of artist.exhibitions" class="flex items-start text-gray-700 text-sm">
            <span class="text-blue-600 mr-2 mt-1 flex-shrink-0">🎨</span>
            <span>{{exhibition}}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Social Media Links -->
    <div *ngIf="artist.socialMedia" class="flex justify-center space-x-4">
      <a *ngIf="artist.socialMedia?.instagram" 
         href="https://instagram.com/{{artist.socialMedia?.instagram}}" 
         target="_blank" 
         class="group">
        <div class="bg-gradient-to-br from-pink-500 to-purple-600 p-3 rounded-full transition-all duration-300 transform group-hover:scale-110 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
          </svg>
        </div>
      </a>

      <a *ngIf="artist.socialMedia?.facebook" 
         href="https://facebook.com/{{artist.socialMedia?.facebook}}" 
         target="_blank" 
         class="group">
        <div class="bg-blue-600 p-3 rounded-full transition-all duration-300 transform group-hover:scale-110 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
        </div>
      </a>

      <a *ngIf="artist.socialMedia?.website" 
         href="https://{{artist.socialMedia?.website}}" 
         target="_blank" 
         class="group">
        <div class="bg-gray-600 p-3 rounded-full transition-all duration-300 transform group-hover:scale-110 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9" />
          </svg>
        </div>
      </a>
    </div>
  </div>
</div>
