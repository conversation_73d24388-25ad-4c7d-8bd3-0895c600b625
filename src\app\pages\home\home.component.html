<!-- Enhanced Hero Section -->
<div class="relative h-[90vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>

  <!-- Animated Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-20">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="hero-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="12" fill="none" stroke="#FFD700" stroke-width="1"/>
          <circle cx="30" cy="30" r="6" fill="none" stroke="#FFD700" stroke-width="1"/>
          <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#FFD700" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#hero-pattern)" />
    </svg>
  </div>

  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-30">
    <div class="absolute w-32 h-32 rounded-full top-[20%] left-[15%] animate-float-slow">
      <div class="absolute inset-0 rounded-full border-2 border-white/30 animate-spin-slow"></div>
    </div>
    <div class="absolute w-20 h-20 rounded-full top-[60%] right-[20%] animate-float-medium">
      <div class="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-full"></div>
    </div>
    <div class="absolute w-16 h-16 rounded-full bottom-[30%] left-[10%] animate-float-fast">
      <div class="absolute inset-0 bg-secondary-400/30 rounded-full"></div>
    </div>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-5xl mx-auto">
      <!-- Animated Logo -->
      <div class="mb-8 relative">
        <div class="inline-block p-4 rounded-full bg-white/10 backdrop-blur-md border border-white/30 shadow-2xl animate-float-slow">
          <div class="w-20 h-20 bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-2xl">M</span>
          </div>
        </div>
      </div>

      <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 font-display animate-fade-in-up">
        {{companyInfo?.name || 'Mithilani Ghar'}}
      </h1>

      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto animate-fade-in-up animation-delay-300">
        {{companyInfo?.tagline || 'Preserving and Promoting the Rich Artistic Heritage of Mithila'}}
      </p>

      <!-- Enhanced Navigation Buttons -->
      <div class="flex flex-wrap justify-center gap-4 mb-12 animate-fade-in-up animation-delay-600">
        <a routerLink="/products"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
          </svg>
          <span class="relative z-10 font-medium">Shop Products</span>
        </a>

        <a routerLink="/gallery"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
          </svg>
          <span class="relative z-10 font-medium">Explore Gallery</span>
        </a>

        <a routerLink="/contact"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          <span class="relative z-10 font-medium">Contact Us</span>
        </a>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="flex flex-col items-center">
          <span class="text-white/80 text-sm mb-2">Scroll Down</span>
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- About Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <app-section-title
      title="Welcome to Mithilani Ghar"
      subtitle="A sanctuary of Mithila art and culture in the heart of Janakpur"
    ></app-section-title>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
      <!-- Left Column: Image with Decorative Border -->
      <div @scaleIn class="relative">
        <!-- Main Image -->
        <div class="rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-rotate-1 hover:scale-105 relative z-10">
          <!-- Gradient Border -->
          <div class="absolute -inset-0.5 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient"></div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg" alt="Mithilani Ghar" class="w-full h-auto transition-transform duration-700 hover:scale-105">

            <!-- Overlay Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute -bottom-6 -right-6 w-full h-full border-4 border-dashed border-primary-200 rounded-lg z-0 animate-pulse-slow"></div>

        <!-- Floating Badge -->
        <div class="absolute -top-5 -right-5 bg-white shadow-lg rounded-full p-4 z-20 transform rotate-12 hover:rotate-0 transition-transform duration-300 animate-float-medium">
          <div class="absolute inset-0 rounded-full bg-gradient-to-br from-secondary-100 to-secondary-200 animate-spin-slow"></div>
          <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg" alt="Mithila Art" class="h-16 w-16 rounded-full object-cover relative z-10">
        </div>
      </div>

      <!-- Right Column: Content -->
      <div class="space-y-6">
        <!-- Feature Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8" @staggerIn>
          <!-- Card 1: Art Gallery -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-primary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-primary-100 p-3 rounded-full mr-4 group-hover:bg-primary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">Art Gallery</h3>
                <p class="text-gray-600 text-sm">Showcasing authentic Mithila paintings and crafts</p>
              </div>
            </div>
          </div>

          <!-- Card 2: Craft Store -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-secondary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-secondary-50/0 via-secondary-50/50 to-secondary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-secondary-100 p-3 rounded-full mr-4 group-hover:bg-secondary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-secondary-600 transition-colors duration-300">Craft Store</h3>
                <p class="text-gray-600 text-sm">Handmade products by skilled local artisans</p>
              </div>
            </div>
          </div>

          <!-- Card 3: Training Center -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-accent-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-accent-50/0 via-accent-50/50 to-accent-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-accent-100 p-3 rounded-full mr-4 group-hover:bg-accent-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-accent-600 transition-colors duration-300">Training Center</h3>
                <p class="text-gray-600 text-sm">Learn traditional Mithila art techniques</p>
              </div>
            </div>
          </div>

          <!-- Card 4: Cultural Hub -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-mithila-red transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-red-50/0 via-red-50/50 to-red-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-red-100 p-3 rounded-full mr-4 group-hover:bg-red-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-mithila-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-mithila-red transition-colors duration-300">Cultural Hub</h3>
                <p class="text-gray-600 text-sm">Celebrating the heritage of Mithila region</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="space-y-4" @fadeSlideIn>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-primary-300/0 via-primary-300 to-primary-300/0"></span>
            {{companyInfo?.description || 'Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region.'}}
          </p>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-secondary-300/0 via-secondary-300 to-secondary-300/0"></span>
            {{companyInfo?.mission || 'Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide.'}}
          </p>
        </div>

        <!-- CTA Button -->
        <div class="pt-4" @scaleIn>
          <a routerLink="/about" class="group relative inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 overflow-hidden rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base">
            <!-- Button Shine Animation -->
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

            <span class="relative z-10 flex items-center">
              <span>Learn More About Us</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>


<!-- Featured Products Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Featured Products"
    subtitle="Discover our handpicked collection of authentic Mithila art pieces">
  </app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <!-- Enhanced Product Card -->
    <div
      *ngFor="let product of featuredProducts; let i = index"
      class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 overflow-hidden border border-gray-100 hover:border-primary-200 cursor-pointer animate-fade-in-up"
      [style.animation-delay]="(i * 100) + 'ms'"
      (click)="navigateToProduct(product.slug)">

      <!-- Product Image Container -->
      <div class="relative overflow-hidden aspect-square">
        <img
          [src]="product.images[0]"
          [alt]="product.name"
          class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

        <!-- Image Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Badges -->
        <div class="absolute top-4 left-4 flex flex-col space-y-2">
          <span *ngIf="product.featured"
                class="bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse flex items-center">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Featured
          </span>

          <span *ngIf="product.category"
                class="bg-white/90 backdrop-blur-sm text-primary-600 px-3 py-1 rounded-full text-xs font-medium shadow-md">
            {{product.category}}
          </span>
        </div>

        <!-- Quick Actions -->
        <div class="absolute bottom-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <!-- Quick View Button -->
          <a [routerLink]="['/products', product.slug]"
             class="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 text-gray-600 hover:text-primary-600"
             (click)="$event.stopPropagation()">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </a>
        </div>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#F4B400'"
          [secondaryColor]="'#C1440E'"
          [type]="'lotus'"
          position="absolute -bottom-2 -left-2"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300"
          size="40px">
        </app-mithila-decorative-element>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Title and Artist -->
        <div class="mb-4">
          <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-300 relative">
            <span class="relative">
              {{product.name}}
              <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
            </span>
          </h3>
          <p class="text-sm text-gray-600 flex items-center">
            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            By <span class="text-primary-600 font-medium ml-1">
              <ng-container *ngIf="!isArray(product.artist); else multipleArtists">
                {{product.artist}}
              </ng-container>
              <ng-template #multipleArtists>
                <span *ngFor="let artist of getArtistArray(product.artist); let last = last">
                  {{artist}}<span *ngIf="!last">, </span>
                </span>
              </ng-template>
            </span>
          </p>
        </div>

        <!-- Description -->
        <p class="text-gray-600 mb-4 line-clamp-2">{{product.description}}</p>

        <!-- Details -->
        <div class="space-y-2 mb-4">
          <div class="flex items-center text-sm text-gray-500">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
            </svg>
            {{product.dimensions}}
          </div>
          <div class="flex items-center text-sm text-gray-500">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
            </svg>
            {{product.materials ? product.materials.join(', ') : 'Handcrafted'}}
          </div>
        </div>

        <!-- Price and Actions -->
        <div class="flex items-center justify-between">
          <div class="flex flex-col">
            <span class="text-2xl font-bold text-primary-600 mb-1">₹{{product.price}}</span>
            <span *ngIf="isInCart(product.id)" class="text-sm text-green-600 font-medium flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              In Cart
            </span>
          </div>

          <!-- Add to Cart Button -->
          <button *ngIf="!isInCart(product.id)"
                  (click)="addToCart(product, $event)"
                  class="px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
            </svg>
            Add to Cart
          </button>

          <!-- Already in Cart Button -->
          <button *ngIf="isInCart(product.id)"
                  class="px-6 py-3 bg-green-600 text-white rounded-lg font-medium flex items-center cursor-default">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            Added to Cart
          </button>
        </div>
      </div>

      <!-- Hover Glow Effect -->
      <div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
           style="box-shadow: 0 0 30px rgba(193, 68, 14, 0.2);"></div>
    </div>
  </div>

  <!-- View All Products Button -->
  <div class="text-center mt-12">
    <a routerLink="/products"
       class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full hover:from-primary-600 hover:to-primary-700 transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
      <span>View All Products</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
      </svg>
    </a>
  </div>
</app-mithila-section>

<!-- Artist Spotlight Section -->
<app-mithila-section *ngIf="spotlightArtist"
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Artist Spotlight"
    [subtitle]="'Featuring ' + spotlightArtist.name + ', ' + spotlightArtist.role">
  </app-section-title>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
        </div>

        <div class="relative rounded-lg overflow-hidden">
          <img [src]="spotlightArtist.image" [alt]="spotlightArtist.name"
               class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          </div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">{{spotlightArtist.name}}</h3>
      <p class="text-primary-600 mb-6 font-medium">{{spotlightArtist.role}}</p>

      <div class="space-y-4 text-gray-700">
        <p>{{spotlightArtist.description}}</p>
      </div>

      <div class="mt-8" *ngIf="spotlightArtist.awards && spotlightArtist.awards.length > 0">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Notable Achievements</h4>
        <ul class="space-y-2 text-gray-700">
          <li *ngFor="let award of spotlightArtist.awards.slice(0, 3)" class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{{award}}</span>
          </li>
        </ul>
      </div>

      <div class="mt-8">
        <a [routerLink]="['/artists', spotlightArtist.id]"
           class="inline-block px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          View Full Profile
        </a>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- CTA Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-500 via-primary-600 to-primary-700"
  backgroundOpacity="20"
  padding="py-16 sm:py-20 md:py-24"
  classes="bg-primary-500"
  [showDecorativeElements]="true">

  <div class="text-center text-white" @fadeIn>
    <app-section-title
      title="Experience the Beauty of Mithila Art"
      subtitle="Visit our gallery in Janakpur or shop online to bring home a piece of this rich cultural heritage"
      classes="text-white"
    ></app-section-title>

    <div class="flex flex-col sm:flex-row justify-center gap-4 mt-8" @staggerIn>
      <a routerLink="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Visit Our Gallery
        </span>
      </a>
      <a routerLink="/products" class="btn bg-secondary-500 text-white hover:bg-secondary-600 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Shop Online
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>
